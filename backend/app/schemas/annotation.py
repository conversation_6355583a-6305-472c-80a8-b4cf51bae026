"""
标注相关的Pydantic schemas
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum

from .question import QuestionResponse
from .knowledge import KnowledgePointResponse


class AnnotationSource(int, Enum):
    """标注来源枚举"""
    EXPERT = 0      # 专家标注
    ALGORITHM = 1   # 算法推断
    IMPORT = 2      # 数据导入


class TaskStatus(int, Enum):
    """任务状态枚举"""
    PENDING = 0      # 待处理
    IN_PROGRESS = 1  # 进行中
    COMPLETED = 2    # 已完成
    UNDER_REVIEW = 3 # 审核中
    REVIEWED = 4     # 已审核
    REJECTED = 5     # 已拒绝
    CANCELLED = 6    # 已取消


class ReviewStatus(int, Enum):
    """审核状态枚举"""
    PENDING = 0      # 待审核
    APPROVED = 1     # 审核通过
    REJECTED = 2     # 审核拒绝
    NEEDS_REVISION = 3  # 需要修改


class TaskType(int, Enum):
    """任务类型枚举"""
    ITEM_KP_MAPPING = 0      # 题目-知识点映射
    KP_PREREQUISITE = 1      # 知识点先修关系
    ITEM_RELATION = 2        # 题目关系标注
    DIFFICULTY_RATING = 3    # 难度评级


class ItemKpMapBase(BaseModel):
    """题目-知识点映射基础字段"""
    question_id: int = Field(..., description="题目ID")
    kp_id: int = Field(..., description="知识点ID")
    is_required: bool = Field(True, description="是否必需掌握")
    weight: float = Field(1.0, ge=0.0, description="权重")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="置信度")
    source: int = Field(0, ge=0, le=2, description="标注来源")


class ItemKpMapCreate(ItemKpMapBase):
    """创建题目-知识点映射的请求模型"""
    pass


class ItemKpMapUpdate(BaseModel):
    """更新题目-知识点映射的请求模型"""
    is_required: Optional[bool] = Field(None, description="是否必需掌握")
    weight: Optional[float] = Field(None, ge=0.0, description="权重")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度")
    source: Optional[int] = Field(None, ge=0, le=2, description="标注来源")


class ItemKpMapResponse(ItemKpMapBase):
    """题目-知识点映射响应模型"""
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联信息
    question: Optional[QuestionResponse] = Field(None, description="题目信息")
    knowledge_point: Optional[KnowledgePointResponse] = Field(None, description="知识点信息")
    
    class Config:
        from_attributes = True


class ItemKpMapList(BaseModel):
    """题目-知识点映射列表响应模型"""
    items: List[ItemKpMapResponse] = Field(..., description="映射列表")
    total: int = Field(..., description="总数")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class AnnotationTaskBase(BaseModel):
    """标注任务基础字段"""
    title: str = Field(..., description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: str = Field(..., description="任务类型")
    priority: int = Field(1, ge=1, le=5, description="优先级")
    deadline: Optional[datetime] = Field(None, description="截止时间")


class AnnotationTaskCreate(AnnotationTaskBase):
    """创建标注任务的请求模型"""
    assignees: List[int] = Field(default_factory=list, description="分配给的用户ID列表")
    question_ids: List[int] = Field(default_factory=list, description="关联的题目ID列表")


class AnnotationTaskUpdate(BaseModel):
    """更新标注任务的请求模型"""
    title: Optional[str] = Field(None, description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    priority: Optional[int] = Field(None, ge=1, le=5, description="优先级")
    deadline: Optional[datetime] = Field(None, description="截止时间")
    status: Optional[int] = Field(None, ge=0, le=3, description="任务状态")
    assignees: Optional[List[int]] = Field(None, description="分配给的用户ID列表")


class AnnotationTaskResponse(AnnotationTaskBase):
    """标注任务响应模型"""
    task_id: int = Field(..., description="任务ID")
    status: int = Field(..., description="任务状态")
    progress: float = Field(..., description="完成进度")
    created_by: int = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    # 审核相关字段
    reviewer_id: Optional[int] = Field(None, description="审核员ID")
    reviewed_at: Optional[datetime] = Field(None, description="审核时间")

    # 统计信息
    total_questions: int = Field(0, description="总题目数")
    completed_questions: int = Field(0, description="已完成题目数")

    class Config:
        from_attributes = True


class AnnotationTaskList(BaseModel):
    """标注任务列表响应模型"""
    items: List[AnnotationTaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class AnnotationLogBase(BaseModel):
    """标注日志基础字段"""
    task_id: int = Field(..., description="任务ID")
    question_id: int = Field(..., description="题目ID")
    action: str = Field(..., description="操作类型")
    old_value: Optional[Dict[str, Any]] = Field(None, description="旧值")
    new_value: Optional[Dict[str, Any]] = Field(None, description="新值")
    comment: Optional[str] = Field(None, description="备注")


class AnnotationLogCreate(AnnotationLogBase):
    """创建标注日志的请求模型"""
    pass


class AnnotationLogResponse(AnnotationLogBase):
    """标注日志响应模型"""
    log_id: int = Field(..., description="日志ID")
    operator_id: int = Field(..., description="操作者ID")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class BatchAnnotationRequest(BaseModel):
    """批量标注请求"""
    question_ids: List[int] = Field(..., description="题目ID列表")
    kp_id: int = Field(..., description="知识点ID")
    is_required: bool = Field(True, description="是否必需掌握")
    weight: float = Field(1.0, ge=0.0, description="权重")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="置信度")
    source: int = Field(0, ge=0, le=2, description="标注来源")


class BatchAnnotationResponse(BaseModel):
    """批量标注响应"""
    success_count: int = Field(..., description="成功数量")
    error_count: int = Field(..., description="失败数量")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")


class AnnotationStats(BaseModel):
    """标注统计信息"""
    total_questions: int = Field(..., description="总题目数")
    annotated_questions: int = Field(..., description="已标注题目数")
    total_mappings: int = Field(..., description="总映射数")
    avg_mappings_per_question: float = Field(..., description="平均每题映射数")
    annotation_coverage: float = Field(..., description="标注覆盖率")


class QMatrixExport(BaseModel):
    """Q矩阵导出格式"""
    questions: List[QuestionResponse] = Field(..., description="题目列表")
    knowledge_points: List[KnowledgePointResponse] = Field(..., description="知识点列表")
    matrix: List[List[float]] = Field(..., description="Q矩阵数据")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class AnnotationValidation(BaseModel):
    """标注验证结果"""
    is_valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    warnings: List[str] = Field(default_factory=list, description="警告信息")
    suggestions: List[str] = Field(default_factory=list, description="建议")


# 审核相关schemas
class AnnotationReviewBase(BaseModel):
    """审核记录基础字段"""
    review_status: int = Field(..., description="审核状态")
    review_comment: Optional[str] = Field(None, description="审核意见")
    quality_score: Optional[int] = Field(None, ge=1, le=10, description="质量评分")
    suggestions: Optional[str] = Field(None, description="改进建议")


class AnnotationReviewCreate(AnnotationReviewBase):
    """创建审核记录的请求模型"""
    task_id: int = Field(..., description="任务ID")
    issues_found: Optional[Dict[str, Any]] = Field(None, description="发现的问题")


class AnnotationReviewUpdate(BaseModel):
    """更新审核记录的请求模型"""
    review_status: Optional[int] = Field(None, description="审核状态")
    review_comment: Optional[str] = Field(None, description="审核意见")
    quality_score: Optional[int] = Field(None, ge=1, le=10, description="质量评分")
    suggestions: Optional[str] = Field(None, description="改进建议")
    issues_found: Optional[Dict[str, Any]] = Field(None, description="发现的问题")


class AnnotationReviewResponse(AnnotationReviewBase):
    """审核记录响应模型"""
    review_id: int = Field(..., description="审核ID")
    task_id: int = Field(..., description="任务ID")
    reviewer_id: int = Field(..., description="审核员ID")
    issues_found: Optional[Dict[str, Any]] = Field(None, description="发现的问题")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class TaskProgressResponse(BaseModel):
    """任务进度响应模型"""
    task_id: int = Field(..., description="任务ID")
    progress: float = Field(..., ge=0.0, le=1.0, description="完成进度")
    total_items: int = Field(..., description="总项目数")
    completed_items: int = Field(..., description="已完成项目数")
    status: int = Field(..., description="任务状态")

    class Config:
        from_attributes = True


class TaskWorkflowAction(BaseModel):
    """任务工作流操作"""
    action: str = Field(..., description="操作类型")
    comment: Optional[str] = Field(None, description="操作备注")
    reviewer_id: Optional[int] = Field(None, description="审核员ID")

    @validator('action')
    def validate_action(cls, v):
        allowed_actions = ['start', 'submit', 'approve', 'reject', 'request_revision', 'cancel']
        if v not in allowed_actions:
            raise ValueError(f'Action must be one of: {allowed_actions}')
        return v
