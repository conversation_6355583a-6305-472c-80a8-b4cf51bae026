"""
通知服务类
"""

from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import json

from app.models.annotation import AnnotationTask, TaskStatus
from app.models.user import User
from app.core.config import settings


class NotificationService:
    """通知服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def send_task_assignment_notification(self, task_id: int, assignee_id: int) -> bool:
        """发送任务分配通知"""
        try:
            task = self.db.query(AnnotationTask).filter(
                AnnotationTask.task_id == task_id
            ).first()
            
            if not task:
                return False
            
            assignee = self.db.query(User).filter(
                User.user_id == assignee_id
            ).first()
            
            if not assignee:
                return False
            
            notification_data = {
                'type': 'task_assignment',
                'task_id': task_id,
                'task_title': task.title,
                'assignee_id': assignee_id,
                'assignee_name': assignee.username,
                'deadline': task.deadline.isoformat() if task.deadline else None,
                'priority': task.priority,
                'message': f'您有新的标注任务：{task.title}',
                'timestamp': datetime.now().isoformat()
            }
            
            # 这里可以集成实际的通知系统（邮件、短信、推送等）
            self._send_notification(assignee_id, notification_data)
            
            return True
            
        except Exception as e:
            print(f"发送任务分配通知失败: {e}")
            return False
    
    def send_review_assignment_notification(self, task_id: int, reviewer_id: int) -> bool:
        """发送审核分配通知"""
        try:
            task = self.db.query(AnnotationTask).filter(
                AnnotationTask.task_id == task_id
            ).first()
            
            if not task:
                return False
            
            reviewer = self.db.query(User).filter(
                User.user_id == reviewer_id
            ).first()
            
            if not reviewer:
                return False
            
            notification_data = {
                'type': 'review_assignment',
                'task_id': task_id,
                'task_title': task.title,
                'reviewer_id': reviewer_id,
                'reviewer_name': reviewer.username,
                'message': f'您有新的审核任务：{task.title}',
                'timestamp': datetime.now().isoformat()
            }
            
            self._send_notification(reviewer_id, notification_data)
            
            return True
            
        except Exception as e:
            print(f"发送审核分配通知失败: {e}")
            return False
    
    def send_review_result_notification(self, task_id: int, review_status: int, review_comment: Optional[str] = None) -> bool:
        """发送审核结果通知"""
        try:
            task = self.db.query(AnnotationTask).filter(
                AnnotationTask.task_id == task_id
            ).first()
            
            if not task or not task.assigned_to:
                return False
            
            assignee = self.db.query(User).filter(
                User.user_id == task.assigned_to
            ).first()
            
            if not assignee:
                return False
            
            status_messages = {
                1: '审核通过',
                2: '审核拒绝',
                3: '需要修改'
            }
            
            status_message = status_messages.get(review_status, '审核完成')
            
            notification_data = {
                'type': 'review_result',
                'task_id': task_id,
                'task_title': task.title,
                'assignee_id': task.assigned_to,
                'assignee_name': assignee.username,
                'review_status': review_status,
                'status_message': status_message,
                'review_comment': review_comment,
                'message': f'您的任务《{task.title}》{status_message}',
                'timestamp': datetime.now().isoformat()
            }
            
            self._send_notification(task.assigned_to, notification_data)
            
            return True
            
        except Exception as e:
            print(f"发送审核结果通知失败: {e}")
            return False
    
    def send_deadline_reminder(self, task_id: int, hours_before: int = 24) -> bool:
        """发送截止时间提醒"""
        try:
            task = self.db.query(AnnotationTask).filter(
                AnnotationTask.task_id == task_id
            ).first()
            
            if not task or not task.assigned_to or not task.deadline:
                return False
            
            # 检查是否需要发送提醒
            reminder_time = task.deadline - timedelta(hours=hours_before)
            if datetime.now() < reminder_time:
                return False
            
            assignee = self.db.query(User).filter(
                User.user_id == task.assigned_to
            ).first()
            
            if not assignee:
                return False
            
            hours_left = (task.deadline - datetime.now()).total_seconds() / 3600
            
            notification_data = {
                'type': 'deadline_reminder',
                'task_id': task_id,
                'task_title': task.title,
                'assignee_id': task.assigned_to,
                'assignee_name': assignee.username,
                'deadline': task.deadline.isoformat(),
                'hours_left': max(0, hours_left),
                'message': f'任务《{task.title}》即将到期，请及时完成',
                'timestamp': datetime.now().isoformat()
            }
            
            self._send_notification(task.assigned_to, notification_data)
            
            return True
            
        except Exception as e:
            print(f"发送截止时间提醒失败: {e}")
            return False
    
    def send_overdue_notification(self, task_id: int) -> bool:
        """发送任务超期通知"""
        try:
            task = self.db.query(AnnotationTask).filter(
                AnnotationTask.task_id == task_id
            ).first()
            
            if not task or not task.assigned_to:
                return False
            
            assignee = self.db.query(User).filter(
                User.user_id == task.assigned_to
            ).first()
            
            if not assignee:
                return False
            
            notification_data = {
                'type': 'task_overdue',
                'task_id': task_id,
                'task_title': task.title,
                'assignee_id': task.assigned_to,
                'assignee_name': assignee.username,
                'deadline': task.deadline.isoformat() if task.deadline else None,
                'message': f'任务《{task.title}》已超期，请尽快处理',
                'timestamp': datetime.now().isoformat()
            }
            
            self._send_notification(task.assigned_to, notification_data)
            
            # 同时通知管理员
            self._notify_managers_about_overdue_task(task_id, notification_data)
            
            return True
            
        except Exception as e:
            print(f"发送超期通知失败: {e}")
            return False
    
    def check_and_send_deadline_reminders(self) -> int:
        """检查并发送截止时间提醒"""
        try:
            # 查找即将到期的任务（24小时内）
            upcoming_deadline = datetime.now() + timedelta(hours=24)
            
            tasks = self.db.query(AnnotationTask).filter(
                AnnotationTask.deadline <= upcoming_deadline,
                AnnotationTask.deadline > datetime.now(),
                AnnotationTask.status.in_([TaskStatus.PENDING.value, TaskStatus.IN_PROGRESS.value])
            ).all()
            
            sent_count = 0
            for task in tasks:
                if self.send_deadline_reminder(task.task_id):
                    sent_count += 1
            
            return sent_count
            
        except Exception as e:
            print(f"检查截止时间提醒失败: {e}")
            return 0
    
    def check_and_send_overdue_notifications(self) -> int:
        """检查并发送超期通知"""
        try:
            # 查找已超期的任务
            overdue_tasks = self.db.query(AnnotationTask).filter(
                AnnotationTask.deadline < datetime.now(),
                AnnotationTask.status.in_([TaskStatus.PENDING.value, TaskStatus.IN_PROGRESS.value])
            ).all()
            
            sent_count = 0
            for task in overdue_tasks:
                if self.send_overdue_notification(task.task_id):
                    sent_count += 1
            
            return sent_count
            
        except Exception as e:
            print(f"检查超期通知失败: {e}")
            return 0
    
    def _send_notification(self, user_id: int, notification_data: Dict[str, Any]) -> bool:
        """发送通知的内部方法"""
        try:
            # 这里可以集成实际的通知系统
            # 例如：邮件、短信、推送通知、WebSocket等
            
            # 示例：打印到控制台（开发环境）
            if settings.ENVIRONMENT == "development":
                print(f"通知发送给用户 {user_id}: {notification_data['message']}")
            
            # 示例：发送到消息队列
            # self._send_to_message_queue(user_id, notification_data)
            
            # 示例：发送邮件
            # self._send_email_notification(user_id, notification_data)
            
            # 示例：发送WebSocket消息
            # self._send_websocket_notification(user_id, notification_data)
            
            return True
            
        except Exception as e:
            print(f"发送通知失败: {e}")
            return False
    
    def _notify_managers_about_overdue_task(self, task_id: int, task_data: Dict[str, Any]) -> bool:
        """通知管理员关于超期任务"""
        try:
            # 查找管理员用户
            managers = self.db.query(User).filter(
                User.role.in_(['admin', 'manager'])
            ).all()
            
            for manager in managers:
                manager_notification = {
                    **task_data,
                    'type': 'manager_overdue_alert',
                    'manager_id': manager.user_id,
                    'message': f'任务《{task_data["task_title"]}》已超期，需要关注'
                }
                self._send_notification(manager.user_id, manager_notification)
            
            return True
            
        except Exception as e:
            print(f"通知管理员失败: {e}")
            return False
