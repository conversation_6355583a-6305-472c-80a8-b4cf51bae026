"""Add annotation review workflow

Revision ID: 004
Revises: 003
Create Date: 2024-01-14 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 添加审核员字段到annotation_tasks表
    op.add_column('annotation_tasks', sa.Column('reviewer_id', sa.BigInteger(), nullable=True))
    op.add_column('annotation_tasks', sa.Column('reviewed_at', sa.DateTime(), nullable=True))
    
    # 创建外键约束
    op.create_foreign_key(
        'fk_annotation_tasks_reviewer_id',
        'annotation_tasks', 'users',
        ['reviewer_id'], ['user_id']
    )
    
    # 添加索引
    op.create_index('idx_annotation_tasks_reviewer_id', 'annotation_tasks', ['reviewer_id'])
    
    # 创建annotation_reviews表
    op.create_table(
        'annotation_reviews',
        sa.Column('review_id', sa.<PERSON>ger(), autoincrement=True, nullable=False),
        sa.Column('task_id', sa.BigInteger(), nullable=False),
        sa.Column('reviewer_id', sa.BigInteger(), nullable=False),
        sa.Column('review_status', sa.SmallInteger(), nullable=False),
        sa.Column('review_comment', sa.Text(), nullable=True),
        sa.Column('quality_score', sa.SmallInteger(), nullable=True),
        sa.Column('issues_found', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('suggestions', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('created_by', sa.BigInteger(), nullable=False),
        sa.Column('updated_by', sa.BigInteger(), nullable=False),
        sa.PrimaryKeyConstraint('review_id'),
        comment='标注审核记录表'
    )
    
    # 创建外键约束
    op.create_foreign_key(
        'fk_annotation_reviews_task_id',
        'annotation_reviews', 'annotation_tasks',
        ['task_id'], ['task_id']
    )
    op.create_foreign_key(
        'fk_annotation_reviews_reviewer_id',
        'annotation_reviews', 'users',
        ['reviewer_id'], ['user_id']
    )
    op.create_foreign_key(
        'fk_annotation_reviews_created_by',
        'annotation_reviews', 'users',
        ['created_by'], ['user_id']
    )
    op.create_foreign_key(
        'fk_annotation_reviews_updated_by',
        'annotation_reviews', 'users',
        ['updated_by'], ['user_id']
    )
    
    # 创建检查约束
    op.create_check_constraint(
        'ck_annotation_reviews_status',
        'annotation_reviews',
        'review_status IN (0, 1, 2, 3)'
    )
    op.create_check_constraint(
        'ck_annotation_reviews_quality_score',
        'annotation_reviews',
        'quality_score >= 1 AND quality_score <= 10'
    )
    
    # 创建索引
    op.create_index('idx_annotation_reviews_task_id', 'annotation_reviews', ['task_id'])
    op.create_index('idx_annotation_reviews_reviewer_id', 'annotation_reviews', ['reviewer_id'])
    op.create_index('idx_annotation_reviews_status', 'annotation_reviews', ['review_status'])
    op.create_index('idx_annotation_reviews_created_at', 'annotation_reviews', ['created_at'])
    
    # 更新annotation_tasks表的状态约束
    op.drop_constraint('ck_annotation_tasks_status', 'annotation_tasks')
    op.create_check_constraint(
        'ck_annotation_tasks_status',
        'annotation_tasks',
        'status IN (0, 1, 2, 3, 4, 5, 6)'
    )
    
    # 更新annotation_tasks表的任务类型约束
    op.drop_constraint('ck_annotation_tasks_task_type', 'annotation_tasks')
    op.create_check_constraint(
        'ck_annotation_tasks_task_type',
        'annotation_tasks',
        'task_type IN (0, 1, 2, 3)'
    )


def downgrade() -> None:
    # 删除索引
    op.drop_index('idx_annotation_reviews_created_at', 'annotation_reviews')
    op.drop_index('idx_annotation_reviews_status', 'annotation_reviews')
    op.drop_index('idx_annotation_reviews_reviewer_id', 'annotation_reviews')
    op.drop_index('idx_annotation_reviews_task_id', 'annotation_reviews')
    op.drop_index('idx_annotation_tasks_reviewer_id', 'annotation_tasks')
    
    # 删除检查约束
    op.drop_constraint('ck_annotation_reviews_quality_score', 'annotation_reviews')
    op.drop_constraint('ck_annotation_reviews_status', 'annotation_reviews')
    
    # 删除外键约束
    op.drop_constraint('fk_annotation_reviews_updated_by', 'annotation_reviews')
    op.drop_constraint('fk_annotation_reviews_created_by', 'annotation_reviews')
    op.drop_constraint('fk_annotation_reviews_reviewer_id', 'annotation_reviews')
    op.drop_constraint('fk_annotation_reviews_task_id', 'annotation_reviews')
    op.drop_constraint('fk_annotation_tasks_reviewer_id', 'annotation_tasks')
    
    # 删除表
    op.drop_table('annotation_reviews')
    
    # 删除列
    op.drop_column('annotation_tasks', 'reviewed_at')
    op.drop_column('annotation_tasks', 'reviewer_id')
    
    # 恢复原来的约束
    op.drop_constraint('ck_annotation_tasks_task_type', 'annotation_tasks')
    op.create_check_constraint(
        'ck_annotation_tasks_task_type',
        'annotation_tasks',
        'task_type IN (0, 1, 2)'
    )
    
    op.drop_constraint('ck_annotation_tasks_status', 'annotation_tasks')
    op.create_check_constraint(
        'ck_annotation_tasks_status',
        'annotation_tasks',
        'status IN (0, 1, 2, 3)'
    )
