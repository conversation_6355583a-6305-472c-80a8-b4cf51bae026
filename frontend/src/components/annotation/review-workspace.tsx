/**
 * 审核工作台组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { CheckCircle, XCircle, AlertCircle, Clock, Eye } from 'lucide-react'
import { toast } from 'sonner'

interface AnnotationTask {
  taskId: number
  title: string
  description: string
  taskType: number
  status: number
  priority: number
  assignedTo: number
  reviewerId?: number
  deadline: string
  progress: number
  createdAt: string
  updatedAt: string
  reviewedAt?: string
}

interface ReviewRecord {
  reviewId: number
  taskId: number
  reviewerId: number
  reviewStatus: number
  reviewComment?: string
  qualityScore?: number
  suggestions?: string
  issuesFound?: any
  createdAt: string
}

interface ReviewWorkspaceProps {
  onTaskSelect?: (task: AnnotationTask) => void
  onReviewSubmit?: (taskId: number, review: any) => void
}

const taskStatusMap = {
  0: { label: '待处理', color: 'bg-gray-500' },
  1: { label: '进行中', color: 'bg-blue-500' },
  2: { label: '已完成', color: 'bg-green-500' },
  3: { label: '审核中', color: 'bg-yellow-500' },
  4: { label: '已审核', color: 'bg-purple-500' },
  5: { label: '已拒绝', color: 'bg-red-500' },
  6: { label: '已取消', color: 'bg-gray-400' }
}

const reviewStatusMap = {
  0: { label: '待审核', color: 'bg-yellow-500', icon: Clock },
  1: { label: '审核通过', color: 'bg-green-500', icon: CheckCircle },
  2: { label: '审核拒绝', color: 'bg-red-500', icon: XCircle },
  3: { label: '需要修改', color: 'bg-orange-500', icon: AlertCircle }
}

export function ReviewWorkspace({ onTaskSelect, onReviewSubmit }: ReviewWorkspaceProps) {
  const [pendingTasks, setPendingTasks] = useState<AnnotationTask[]>([])
  const [selectedTask, setSelectedTask] = useState<AnnotationTask | null>(null)
  const [reviewHistory, setReviewHistory] = useState<ReviewRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  
  // 审核表单状态
  const [reviewStatus, setReviewStatus] = useState<string>('')
  const [reviewComment, setReviewComment] = useState('')
  const [qualityScore, setQualityScore] = useState<string>('')
  const [suggestions, setSuggestions] = useState('')

  useEffect(() => {
    loadPendingTasks()
  }, [])

  useEffect(() => {
    if (selectedTask) {
      loadReviewHistory(selectedTask.taskId)
    }
  }, [selectedTask])

  const loadPendingTasks = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      const mockTasks: AnnotationTask[] = [
        {
          taskId: 1,
          title: '英语语法知识点标注',
          description: '标注英语语法相关题目的知识点映射关系',
          taskType: 0,
          status: 3,
          priority: 3,
          assignedTo: 2,
          reviewerId: 1,
          deadline: '2024-01-15T23:59:59Z',
          progress: 100,
          createdAt: '2024-01-10T09:00:00Z',
          updatedAt: '2024-01-12T15:30:00Z'
        },
        {
          taskId: 2,
          title: '数学函数关系标注',
          description: '标注数学函数题目间的关系',
          taskType: 2,
          status: 3,
          priority: 4,
          assignedTo: 3,
          reviewerId: 1,
          deadline: '2024-01-20T23:59:59Z',
          progress: 100,
          createdAt: '2024-01-11T10:00:00Z',
          updatedAt: '2024-01-13T16:45:00Z'
        }
      ]
      setPendingTasks(mockTasks)
    } catch (error) {
      toast.error('加载待审核任务失败')
    } finally {
      setLoading(false)
    }
  }

  const loadReviewHistory = async (taskId: number) => {
    try {
      // 模拟API调用
      const mockHistory: ReviewRecord[] = [
        {
          reviewId: 1,
          taskId: taskId,
          reviewerId: 1,
          reviewStatus: 0,
          reviewComment: '正在审核中...',
          createdAt: '2024-01-13T09:00:00Z'
        }
      ]
      setReviewHistory(mockHistory)
    } catch (error) {
      toast.error('加载审核历史失败')
    }
  }

  const handleTaskSelect = (task: AnnotationTask) => {
    setSelectedTask(task)
    onTaskSelect?.(task)
    
    // 重置审核表单
    setReviewStatus('')
    setReviewComment('')
    setQualityScore('')
    setSuggestions('')
  }

  const handleSubmitReview = async () => {
    if (!selectedTask || !reviewStatus) {
      toast.error('请选择审核结果')
      return
    }

    setSubmitting(true)
    try {
      const reviewData = {
        taskId: selectedTask.taskId,
        reviewStatus: parseInt(reviewStatus),
        reviewComment: reviewComment || undefined,
        qualityScore: qualityScore ? parseInt(qualityScore) : undefined,
        suggestions: suggestions || undefined
      }

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('审核提交成功')
      onReviewSubmit?.(selectedTask.taskId, reviewData)
      
      // 刷新数据
      loadPendingTasks()
      if (selectedTask) {
        loadReviewHistory(selectedTask.taskId)
      }
      
      // 重置表单
      setReviewStatus('')
      setReviewComment('')
      setQualityScore('')
      setSuggestions('')
      
    } catch (error) {
      toast.error('审核提交失败')
    } finally {
      setSubmitting(false)
    }
  }

  const getStatusBadge = (status: number) => {
    const statusInfo = taskStatusMap[status as keyof typeof taskStatusMap]
    return (
      <Badge className={`${statusInfo.color} text-white`}>
        {statusInfo.label}
      </Badge>
    )
  }

  const getReviewStatusBadge = (status: number) => {
    const statusInfo = reviewStatusMap[status as keyof typeof reviewStatusMap]
    const Icon = statusInfo.icon
    return (
      <Badge className={`${statusInfo.color} text-white flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {statusInfo.label}
      </Badge>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 待审核任务列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            待审核任务
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : pendingTasks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">暂无待审核任务</div>
          ) : (
            <div className="space-y-4">
              {pendingTasks.map((task) => (
                <div
                  key={task.taskId}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedTask?.taskId === task.taskId
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleTaskSelect(task)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{task.title}</h3>
                    {getStatusBadge(task.status)}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{task.description}</p>
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>优先级: {task.priority}</span>
                    <span>截止: {new Date(task.deadline).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 审核详情和操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5" />
            审核详情
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!selectedTask ? (
            <div className="text-center py-8 text-gray-500">
              请选择要审核的任务
            </div>
          ) : (
            <div className="space-y-6">
              {/* 任务信息 */}
              <div>
                <h3 className="font-medium mb-2">{selectedTask.title}</h3>
                <p className="text-sm text-gray-600 mb-4">{selectedTask.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">状态:</span> {getStatusBadge(selectedTask.status)}
                  </div>
                  <div>
                    <span className="text-gray-500">进度:</span> {selectedTask.progress}%
                  </div>
                  <div>
                    <span className="text-gray-500">优先级:</span> {selectedTask.priority}
                  </div>
                  <div>
                    <span className="text-gray-500">截止时间:</span> {new Date(selectedTask.deadline).toLocaleDateString()}
                  </div>
                </div>
              </div>

              {/* 审核表单 */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="reviewStatus">审核结果 *</Label>
                  <Select value={reviewStatus} onValueChange={setReviewStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择审核结果" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">审核通过</SelectItem>
                      <SelectItem value="2">审核拒绝</SelectItem>
                      <SelectItem value="3">需要修改</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="qualityScore">质量评分 (1-10)</Label>
                  <Select value={qualityScore} onValueChange={setQualityScore}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择质量评分" />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(10)].map((_, i) => (
                        <SelectItem key={i + 1} value={String(i + 1)}>
                          {i + 1}分
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="reviewComment">审核意见</Label>
                  <Textarea
                    id="reviewComment"
                    placeholder="请输入审核意见..."
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="suggestions">改进建议</Label>
                  <Textarea
                    id="suggestions"
                    placeholder="请输入改进建议..."
                    value={suggestions}
                    onChange={(e) => setSuggestions(e.target.value)}
                    rows={3}
                  />
                </div>

                <Button 
                  onClick={handleSubmitReview}
                  disabled={!reviewStatus || submitting}
                  className="w-full"
                >
                  {submitting ? '提交中...' : '提交审核'}
                </Button>
              </div>

              {/* 审核历史 */}
              {reviewHistory.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">审核历史</h4>
                  <div className="space-y-2">
                    {reviewHistory.map((review) => (
                      <div key={review.reviewId} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          {getReviewStatusBadge(review.reviewStatus)}
                          <span className="text-xs text-gray-500">
                            {new Date(review.createdAt).toLocaleString()}
                          </span>
                        </div>
                        {review.reviewComment && (
                          <p className="text-sm text-gray-600">{review.reviewComment}</p>
                        )}
                        {review.qualityScore && (
                          <p className="text-xs text-gray-500 mt-1">
                            质量评分: {review.qualityScore}/10
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
