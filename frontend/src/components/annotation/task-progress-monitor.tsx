/**
 * 任务进度监控组件
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line
} from 'recharts'
import { 
  Activity, Clock, CheckCircle, AlertTriangle, Users, 
  TrendingUp, Calendar, Target
} from 'lucide-react'

interface TaskStats {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  pendingTasks: number
  overdueTasks: number
  averageCompletionTime: number
  qualityScore: number
}

interface TaskProgress {
  taskId: number
  title: string
  assignee: string
  progress: number
  status: number
  deadline: string
  priority: number
}

interface QualityMetrics {
  period: string
  accuracy: number
  consistency: number
  completeness: number
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

const statusColors = {
  0: 'bg-gray-500',
  1: 'bg-blue-500', 
  2: 'bg-green-500',
  3: 'bg-yellow-500',
  4: 'bg-purple-500',
  5: 'bg-red-500',
  6: 'bg-gray-400'
}

export function TaskProgressMonitor() {
  const [stats, setStats] = useState<TaskStats>({
    totalTasks: 0,
    completedTasks: 0,
    inProgressTasks: 0,
    pendingTasks: 0,
    overdueTasks: 0,
    averageCompletionTime: 0,
    qualityScore: 0
  })
  
  const [taskProgress, setTaskProgress] = useState<TaskProgress[]>([])
  const [qualityMetrics, setQualityMetrics] = useState<QualityMetrics[]>([])
  const [loading, setLoading] = useState(false)
  const [timeRange, setTimeRange] = useState('week')

  useEffect(() => {
    loadData()
  }, [timeRange])

  const loadData = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟统计数据
      setStats({
        totalTasks: 156,
        completedTasks: 89,
        inProgressTasks: 34,
        pendingTasks: 23,
        overdueTasks: 10,
        averageCompletionTime: 3.2,
        qualityScore: 8.7
      })

      // 模拟任务进度数据
      setTaskProgress([
        {
          taskId: 1,
          title: '英语语法知识点标注',
          assignee: '张三',
          progress: 85,
          status: 1,
          deadline: '2024-01-15',
          priority: 3
        },
        {
          taskId: 2,
          title: '数学函数关系标注',
          assignee: '李四',
          progress: 100,
          status: 2,
          deadline: '2024-01-12',
          priority: 4
        },
        {
          taskId: 3,
          title: '物理概念映射',
          assignee: '王五',
          progress: 45,
          status: 1,
          deadline: '2024-01-20',
          priority: 2
        }
      ])

      // 模拟质量指标数据
      setQualityMetrics([
        { period: '第1周', accuracy: 92, consistency: 88, completeness: 95 },
        { period: '第2周', accuracy: 94, consistency: 90, completeness: 93 },
        { period: '第3周', accuracy: 89, consistency: 85, completeness: 97 },
        { period: '第4周', accuracy: 96, consistency: 92, completeness: 94 }
      ])
      
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: number) => {
    const statusLabels = {
      0: '待处理',
      1: '进行中',
      2: '已完成',
      3: '审核中',
      4: '已审核',
      5: '已拒绝',
      6: '已取消'
    }
    
    return (
      <Badge className={`${statusColors[status as keyof typeof statusColors]} text-white`}>
        {statusLabels[status as keyof typeof statusLabels]}
      </Badge>
    )
  }

  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'text-red-600'
    if (priority >= 3) return 'text-yellow-600'
    return 'text-green-600'
  }

  const taskStatusData = [
    { name: '已完成', value: stats.completedTasks, color: '#00C49F' },
    { name: '进行中', value: stats.inProgressTasks, color: '#0088FE' },
    { name: '待处理', value: stats.pendingTasks, color: '#FFBB28' },
    { name: '超期', value: stats.overdueTasks, color: '#FF8042' }
  ]

  const completionRate = stats.totalTasks > 0 ? (stats.completedTasks / stats.totalTasks) * 100 : 0

  return (
    <div className="space-y-6">
      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总任务数</p>
                <p className="text-2xl font-bold">{stats.totalTasks}</p>
              </div>
              <Target className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">完成率</p>
                <p className="text-2xl font-bold">{completionRate.toFixed(1)}%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <Progress value={completionRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均完成时间</p>
                <p className="text-2xl font-bold">{stats.averageCompletionTime}天</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">质量评分</p>
                <p className="text-2xl font-bold">{stats.qualityScore}/10</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 任务状态分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              任务状态分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={taskStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {taskStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 质量趋势 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              质量趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={qualityMetrics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis domain={[0, 100]} />
                <Tooltip />
                <Line type="monotone" dataKey="accuracy" stroke="#8884d8" name="准确性" />
                <Line type="monotone" dataKey="consistency" stroke="#82ca9d" name="一致性" />
                <Line type="monotone" dataKey="completeness" stroke="#ffc658" name="完整性" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 任务进度详情 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            任务进度详情
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : (
            <div className="space-y-4">
              {taskProgress.map((task) => (
                <div key={task.taskId} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-medium">{task.title}</h3>
                      <p className="text-sm text-gray-600">负责人: {task.assignee}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(task.status)}
                      <span className={`text-sm font-medium ${getPriorityColor(task.priority)}`}>
                        P{task.priority}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">进度</span>
                      <span className="text-sm font-medium">{task.progress}%</span>
                    </div>
                    <Progress value={task.progress} />
                    
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        截止: {new Date(task.deadline).toLocaleDateString()}
                      </span>
                      {new Date(task.deadline) < new Date() && task.status !== 2 && (
                        <span className="flex items-center gap-1 text-red-600">
                          <AlertTriangle className="w-4 h-4" />
                          已超期
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
